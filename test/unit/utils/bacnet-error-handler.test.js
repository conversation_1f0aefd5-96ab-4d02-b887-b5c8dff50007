const sinon = require('sinon');
const BACnetErrorHandler = require('../../../api/utils/bacnet/error-handler.util');

describe('BACnet Error Handler Utility', function() {
  let sandbox;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Mock CacheService
    global.CacheService = {
      hmset: sandbox.stub().resolves('OK')
    };
  });

  afterEach(function() {
    sandbox.restore();
    delete global.CacheService;
  });

  describe('Input Validation', function() {
    
    describe('validateDiscoveryRequest', function() {
      
      it('should pass validation for valid inputs', function() {
        // Should not throw
        BACnetErrorHandler.validateDiscoveryRequest('test-site', 'test-controller');
      });

      it('should throw error for missing site ID', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest(null, 'test-controller');
        }).should.throw('Site ID is required and must be a string');
      });

      it('should throw error for empty site ID', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest('', 'test-controller');
        }).should.throw('Site ID is required and must be a string');
      });

      it('should throw error for non-string site ID', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest(123, 'test-controller');
        }).should.throw('Site ID is required and must be a string');
      });

      it('should throw error for missing controller ID', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest('test-site', null);
        }).should.throw('Controller ID is required and must be a string');
      });

      it('should throw error for invalid site ID format', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest('test@site!', 'test-controller');
        }).should.throw('Site ID contains invalid characters');
      });

      it('should throw error for invalid controller ID format', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryRequest('test-site', 'test@controller!');
        }).should.throw('Controller ID contains invalid characters');
      });
    });

    describe('validateDiscoveryResult', function() {
      
      const validPoints = {
        devices: [
          {
            name: 'AHU-01',
            address: '*************:47808'
          }
        ]
      };

      it('should pass validation for valid inputs', function() {
        // Should not throw
        BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', 'test-job-123', validPoints);
      });

      it('should throw error for missing job ID', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', null, validPoints);
        }).should.throw('Job ID is required and must be a string');
      });

      it('should throw error for missing points data', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', 'test-job-123', null);
        }).should.throw('Points data is required and must be an object');
      });

      it('should throw error for missing devices array', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', 'test-job-123', {});
        }).should.throw('Points data must contain a devices array');
      });

      it('should throw error for empty devices array', function() {
        (() => {
          BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', 'test-job-123', { devices: [] });
        }).should.throw('Points data must contain at least one device');
      });

      it('should throw error for device missing required fields', function() {
        const invalidPoints = {
          devices: [
            { name: 'AHU-01' } // Missing address
          ]
        };
        
        (() => {
          BACnetErrorHandler.validateDiscoveryResult('test-site', 'test-controller', 'test-job-123', invalidPoints);
        }).should.throw('Device at index 0 is missing required fields (name, address)');
      });
    });
  });

  describe('Error Handling', function() {
    
    describe('handleDiscoveryRequestError', function() {
      
      it('should handle validation errors', function() {
        const validationError = new Error('Invalid input');
        validationError.name = 'ValidationError';
        
        const result = BACnetErrorHandler.handleDiscoveryRequestError(validationError, 'test-site', 'test-controller');
        
        result.should.have.property('statusCode', 400);
        result.should.have.property('error', 'Validation Error');
        result.should.have.property('message', 'Invalid input');
      });

      it('should handle not found errors', function() {
        const notFoundError = new Error('Site not found');
        notFoundError.name = 'NotFoundError';
        
        const result = BACnetErrorHandler.handleDiscoveryRequestError(notFoundError, 'test-site', 'test-controller');
        
        result.should.have.property('statusCode', 404);
        result.should.have.property('error', 'Resource Not Found');
        result.should.have.property('message', 'Site not found');
      });

      it('should handle conflict errors', function() {
        const conflictError = new Error('Discovery already in progress');
        conflictError.name = 'ConflictError';
        
        const result = BACnetErrorHandler.handleDiscoveryRequestError(conflictError, 'test-site', 'test-controller');
        
        result.should.have.property('statusCode', 409);
        result.should.have.property('error', 'Conflict');
        result.should.have.property('message', 'Discovery already in progress');
      });

      it('should handle generic errors as server errors', function() {
        const genericError = new Error('Something went wrong');
        
        const result = BACnetErrorHandler.handleDiscoveryRequestError(genericError, 'test-site', 'test-controller');
        
        result.should.have.property('statusCode', 500);
        result.should.have.property('error', 'Internal Server Error');
        result.should.have.property('message', 'Failed to process BACnet discovery request');
      });

      it('should include error details in development environment', function() {
        const originalEnv = sails.config.environment;
        sails.config.environment = 'development';
        
        const error = new Error('Test error');
        const result = BACnetErrorHandler.handleDiscoveryRequestError(error, 'test-site', 'test-controller');
        
        result.should.have.property('details', 'Test error');
        
        sails.config.environment = originalEnv;
      });
    });

    describe('handleDiscoveryResultError', function() {
      
      it('should handle discovery result errors and update job status', function() {
        const error = new Error('Processing failed');
        
        const result = BACnetErrorHandler.handleDiscoveryResultError(error, 'test-site', 'test-controller', 'test-job-123');
        
        result.should.have.property('statusCode', 500);
        result.should.have.property('error', 'Processing Error');
        result.should.have.property('message', 'Failed to process BACnet discovery result');
        result.should.have.property('jobId', 'test-job-123');
      });
    });

    describe('handleQueueProcessingError', function() {
      
      it('should categorize database errors', function() {
        const dbError = new Error('Connection failed');
        dbError.name = 'DatabaseError';
        dbError.code = 'ECONNREFUSED';
        
        const result = BACnetErrorHandler.handleQueueProcessingError(dbError, 'test-job-123');
        
        result.should.have.property('type', 'database_error');
        result.should.have.property('message', 'Database operation failed');
        result.should.have.property('code', 'ECONNREFUSED');
      });

      it('should categorize validation errors', function() {
        const validationError = new Error('Invalid data format');
        validationError.name = 'ValidationError';
        
        const result = BACnetErrorHandler.handleQueueProcessingError(validationError, 'test-job-123');
        
        result.should.have.property('type', 'validation_error');
        result.should.have.property('message', 'Data validation failed');
      });

      it('should categorize generic processing errors', function() {
        const genericError = new Error('Unknown error');
        
        const result = BACnetErrorHandler.handleQueueProcessingError(genericError, 'test-job-123');
        
        result.should.have.property('type', 'processing_error');
        result.should.have.property('message', 'Failed to process discovery data');
      });
    });
  });

  describe('Utility Methods', function() {
    
    describe('updateJobStatusToFailed', function() {
      
      it('should update job status in Redis', async function() {
        await BACnetErrorHandler.updateJobStatusToFailed('test-job-123', 'Test error');
        
        sinon.assert.calledOnce(CacheService.hmset);
        const call = CacheService.hmset.getCall(0);
        call.args[0].should.equal('BACnetDiscovery:job:test-job-123');
        call.args[1].should.have.property('status', 'failed');
        call.args[1].should.have.property('error', 'Test error');
        call.args[1].should.have.property('failedAt');
      });

      it('should handle Redis errors gracefully', async function() {
        CacheService.hmset.rejects(new Error('Redis error'));
        
        // Should not throw
        await BACnetErrorHandler.updateJobStatusToFailed('test-job-123', 'Test error');
      });
    });
  });
});
