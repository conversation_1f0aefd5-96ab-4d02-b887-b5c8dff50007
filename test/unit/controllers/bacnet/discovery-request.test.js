const request = require('supertest');
const sinon = require('sinon');

describe('BACnet Discovery Request Controller', function() {
  let sandbox;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('POST /bacnet/discovery-request', function() {
    
    it('should successfully trigger discovery request', function(done) {
      // Mock site exists
      sandbox.stub(sails.models.sites, 'findOne').resolves({
        id: 'test-site',
        siteName: 'Test Site'
      });

      // Mock controller exists
      sandbox.stub(sails.models.devices, 'findOne')
        .onFirstCall().resolves({
          deviceId: 'test-controller',
          siteId: 'test-site',
          isSlaveController: 1,
          deviceType: 'n3uronbacnetmqtt-controller'
        })
        .onSecondCall().resolves({
          deviceId: 'primary-joulebox',
          siteId: 'test-site',
          deviceType: 'joulebox',
          type: 'Primary'
        });

      // Mock Redis operations
      sandbox.stub(CacheService, 'get').resolves(null);
      sandbox.stub(CacheService, 'setex').resolves('OK');
      sandbox.stub(CacheService, 'hmset').resolves('OK');
      sandbox.stub(CacheService, 'expire').resolves(1);

      // Mock IoT Core publish
      sandbox.stub(IotCoreService, 'publish').resolves(true);

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'test-site',
          controllerId: 'test-controller'
        })
        .expect(200)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('message', 'Discovery request triggered successfully');
          res.body.should.have.property('jobId');
          res.body.should.have.property('status', 'pending');
          res.body.should.have.property('timestamp');

          // Verify IoT Core publish was called
          sinon.assert.calledOnce(IotCoreService.publish);
          const publishCall = IotCoreService.publish.getCall(0);
          publishCall.args[0].should.equal('test-site/config/test-controller/discovery-request');

          done();
        });
    });

    it('should return 404 when site not found', function(done) {
      sandbox.stub(sails.models.sites, 'findOne').resolves(null);

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'nonexistent-site',
          controllerId: 'test-controller'
        })
        .expect(404)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Site not found');
          done();
        });
    });

    it('should return 404 when controller not found', function(done) {
      sandbox.stub(sails.models.sites, 'findOne').resolves({
        id: 'test-site',
        siteName: 'Test Site'
      });

      sandbox.stub(sails.models.devices, 'findOne').resolves(null);

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'test-site',
          controllerId: 'nonexistent-controller'
        })
        .expect(404)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'BACnet controller not found');
          done();
        });
    });

    it('should return 409 when discovery already in progress', function(done) {
      sandbox.stub(sails.models.sites, 'findOne').resolves({
        id: 'test-site',
        siteName: 'Test Site'
      });

      sandbox.stub(sails.models.devices, 'findOne').resolves({
        deviceId: 'test-controller',
        siteId: 'test-site',
        isSlaveController: 1
      });

      // Mock existing job in progress
      sandbox.stub(CacheService, 'get').resolves('existing-job-id');

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'test-site',
          controllerId: 'test-controller'
        })
        .expect(409)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Discovery in progress');
          res.body.should.have.property('jobId', 'existing-job-id');
          done();
        });
    });

    it('should return 400 for invalid input validation', function(done) {
      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: '', // Invalid empty string
          controllerId: 'test-controller'
        })
        .expect(400)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });

    it('should handle database errors gracefully', function(done) {
      sandbox.stub(sails.models.sites, 'findOne').rejects(new Error('Database connection failed'));

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'test-site',
          controllerId: 'test-controller'
        })
        .expect(500)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });

    it('should handle IoT Core publish failures', function(done) {
      sandbox.stub(sails.models.sites, 'findOne').resolves({
        id: 'test-site',
        siteName: 'Test Site'
      });

      sandbox.stub(sails.models.devices, 'findOne')
        .onFirstCall().resolves({
          deviceId: 'test-controller',
          siteId: 'test-site',
          isSlaveController: 1
        })
        .onSecondCall().resolves({
          deviceId: 'primary-joulebox',
          siteId: 'test-site',
          deviceType: 'joulebox',
          type: 'Primary'
        });

      sandbox.stub(CacheService, 'get').resolves(null);
      sandbox.stub(CacheService, 'setex').resolves('OK');
      sandbox.stub(CacheService, 'hmset').resolves('OK');
      sandbox.stub(CacheService, 'expire').resolves(1);

      // Mock IoT Core publish failure
      sandbox.stub(IotCoreService, 'publish').rejects(new Error('MQTT connection failed'));

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-request')
        .send({
          siteId: 'test-site',
          controllerId: 'test-controller'
        })
        .expect(500)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });
  });
});
