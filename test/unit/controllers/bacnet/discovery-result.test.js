const request = require('supertest');
const sinon = require('sinon');

describe('BACnet Discovery Result Controller', function() {
  let sandbox;
  let mockBacnetQueue;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Mock the BACnet queue service
    mockBacnetQueue = {
      addJob: sandbox.stub().resolves({ id: 'queue-job-123' })
    };
    
    // Mock require for the queue service
    sandbox.stub(require.cache, '../jt-api-v2/api/services/queue/bacnet-discovery-queue.service').value = mockBacnetQueue;
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('POST /bacnet/discovery-result', function() {
    
    const validDiscoveryData = {
      siteId: 'test-site',
      controllerId: 'test-controller',
      jobId: 'test-job-123',
      points: {
        protocol: 'bacnet',
        version: 'v1',
        devices: [
          {
            name: 'AHU-01',
            address: '*************:47808',
            systemStatus: 'operational',
            description: 'Air Handling Unit 01',
            vendorName: 'Johnson Controls',
            modelName: 'AHU-Model-X',
            location: 'Roof Level 1',
            objects: [
              {
                name: 'Supply Air Temperature',
                address: '*************:47808:analogInput:1',
                description: 'Supply air temperature sensor',
                eventState: 'normal',
                outOfService: 'false',
                reliability: 'noFaultDetected',
                statusFlags: { inAlarm: false, fault: false, overridden: false, outOfService: false },
                protocolNativeUnit: 'degreesCelsius',
                unit: '°C',
                properties: [
                  {
                    propertyTag: 'presentValue',
                    type: 'real',
                    address: '*************:47808:analogInput:1:presentValue',
                    protocolNativeUnit: 'degreesCelsius',
                    value: '22.5'
                  }
                ]
              }
            ]
          }
        ]
      }
    };

    it('should successfully process discovery result', function(done) {
      // Mock Redis operations for job validation
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({
        jobId: 'test-job-123',
        status: 'pending',
        siteId: 'test-site',
        controllerId: 'test-controller'
      });
      sandbox.stub(CacheService, 'hmset').resolves('OK');

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(200)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('message', 'Discovery result received and queued for processing');
          res.body.should.have.property('jobId', 'test-job-123');
          res.body.should.have.property('status', 'processing');
          res.body.should.have.property('devicesCount', 1);

          // Verify queue job was added
          sinon.assert.calledOnce(mockBacnetQueue.addJob);
          const queueCall = mockBacnetQueue.addJob.getCall(0);
          queueCall.args[0].should.have.property('jobId', 'test-job-123');
          queueCall.args[0].should.have.property('siteId', 'test-site');
          queueCall.args[0].should.have.property('controllerId', 'test-controller');

          done();
        });
    });

    it('should return 404 for invalid job ID', function(done) {
      // Mock no active job found
      sandbox.stub(CacheService, 'get').resolves(null);

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send({
          ...validDiscoveryData,
          jobId: 'invalid-job-id'
        })
        .expect(404)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Invalid job');
          done();
        });
    });

    it('should return 404 for expired job', function(done) {
      // Mock active job exists but different ID
      sandbox.stub(CacheService, 'get').resolves('different-job-id');

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(404)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Invalid job');
          done();
        });
    });

    it('should return 404 for non-existent job data', function(done) {
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({}); // Empty job data

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(404)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Job not found');
          done();
        });
    });

    it('should return 400 for job not in pending state', function(done) {
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({
        jobId: 'test-job-123',
        status: 'completed', // Not pending
        siteId: 'test-site',
        controllerId: 'test-controller'
      });

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(400)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Invalid job state');
          done();
        });
    });

    it('should return 400 for invalid points data structure', function(done) {
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({
        jobId: 'test-job-123',
        status: 'pending',
        siteId: 'test-site',
        controllerId: 'test-controller'
      });

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send({
          ...validDiscoveryData,
          points: { invalid: 'data' } // Missing devices array
        })
        .expect(400)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error', 'Invalid points data');
          done();
        });
    });

    it('should return 400 for empty devices array', function(done) {
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({
        jobId: 'test-job-123',
        status: 'pending',
        siteId: 'test-site',
        controllerId: 'test-controller'
      });

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send({
          ...validDiscoveryData,
          points: { devices: [] } // Empty devices array
        })
        .expect(400)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });

    it('should handle Redis errors gracefully', function(done) {
      sandbox.stub(CacheService, 'get').rejects(new Error('Redis connection failed'));

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(500)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });

    it('should handle queue service errors', function(done) {
      sandbox.stub(CacheService, 'get').resolves('test-job-123');
      sandbox.stub(CacheService, 'hgetall').resolves({
        jobId: 'test-job-123',
        status: 'pending',
        siteId: 'test-site',
        controllerId: 'test-controller'
      });
      sandbox.stub(CacheService, 'hmset').resolves('OK');

      // Mock queue service failure
      mockBacnetQueue.addJob.rejects(new Error('Queue service unavailable'));

      request(sails.hooks.http.app)
        .post('/bacnet/discovery-result')
        .send(validDiscoveryData)
        .expect(500)
        .end(function(err, res) {
          if (err) return done(err);

          res.body.should.have.property('error');
          done();
        });
    });
  });
});
