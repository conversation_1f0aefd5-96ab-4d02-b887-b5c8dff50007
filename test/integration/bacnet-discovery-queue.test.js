const sinon = require('sinon');
const BacnetDiscoveryQueue = require('../../api/services/queue/bacnet-discovery-queue.service');

describe('BACnet Discovery Queue Integration', function () {
  let sandbox;
  let queueService;

  beforeEach(function () {
    sandbox = sinon.createSandbox();

    // Mock Redis/Cache operations
    sandbox.stub(CacheService, 'hmset').resolves('OK');
    sandbox.stub(CacheService, 'del').resolves(1);

    // Mock Socket service
    sandbox.stub(SocketService, 'notifyJouleTrackPublicRoom').resolves();

    // Mock database transaction
    const mockTransaction = {
      usingConnection: function (callback) {
        return callback(this);
      }
    };

    sandbox.stub(sails, 'getDatastore').returns({
      transaction: function (callback) {
        return callback(mockTransaction);
      }
    });

    // Mock model operations
    sandbox.stub(sails.models.bmsconnector, 'findOne').resolves(null);
    sandbox.stub(sails.models.bmsconnector, 'create').resolves({ id: 1 });
    sandbox.stub(sails.models.bmsdevice, 'findOne').resolves(null);
    sandbox.stub(sails.models.bmsdevice, 'create').resolves({ id: 1 });
    sandbox.stub(sails.models.bmsdeviceobject, 'findOne').resolves(null);
    sandbox.stub(sails.models.bmsdeviceobject, 'create').resolves({ id: 1 });
    sandbox.stub(sails.models.bmsobjectproperty, 'findOne').resolves(null);
    sandbox.stub(sails.models.bmsobjectproperty, 'create').resolves({ id: 1 });

    queueService = BacnetDiscoveryQueue;
  });

  afterEach(function () {
    sandbox.restore();
  });

  describe('Job Processing', function () {

    const mockJobData = {
      jobId: 'test-job-123',
      siteId: 'test-site',
      controllerId: 'test-controller',
      points: {
        protocol: 'bacnet',
        version: 'v1',
        bmsConnectorClass: 'n3uron/bacnet/mqtt',
        devices: [
          {
            name: 'AHU-01',
            address: '*************:47808',
            systemStatus: 'operational',
            description: 'Air Handling Unit 01',
            vendorName: 'Johnson Controls',
            modelName: 'AHU-Model-X',
            location: 'Roof Level 1',
            objects: [
              {
                name: 'Supply Air Temperature',
                address: '*************:47808:analogInput:1',
                description: 'Supply air temperature sensor',
                eventState: 'normal',
                outOfService: 'false',
                reliability: 'noFaultDetected',
                statusFlags: { inAlarm: false, fault: false, overridden: false, outOfService: false },
                protocolNativeUnit: 'degreesCelsius',
                unit: '°C',
                properties: [
                  {
                    propertyTag: 'presentValue',
                    type: 'real',
                    address: '*************:47808:analogInput:1:presentValue',
                    protocolNativeUnit: 'degreesCelsius',
                    value: '22.5'
                  }
                ]
              }
            ]
          }
        ]
      },
      timestamp: new Date().toISOString()
    };

    it('should successfully process discovery results and persist to database', async function () {
      const mockJob = { id: 'queue-job-123', data: mockJobData };

      const result = await queueService.processJob(mockJob);

      result.should.have.property('success', true);
      result.should.have.property('jobId', 'test-job-123');
      result.should.have.property('devicesProcessed', 1);

      // Verify BMS connector was created
      sinon.assert.calledOnce(sails.models.bmsconnector.create);
      const connectorCall = sails.models.bmsconnector.create.getCall(0);
      connectorCall.args[0].should.have.property('siteId', 'test-site');
      connectorCall.args[0].should.have.property('protocol', 'bacnet');

      // Verify BMS device was created
      sinon.assert.calledOnce(sails.models.bmsdevice.create);
      const deviceCall = sails.models.bmsdevice.create.getCall(0);
      deviceCall.args[0].should.have.property('name', 'AHU-01');
      deviceCall.args[0].should.have.property('address', '*************:47808');

      // Verify BMS device object was created
      sinon.assert.calledOnce(sails.models.bmsdeviceobject.create);
      const objectCall = sails.models.bmsdeviceobject.create.getCall(0);
      objectCall.args[0].should.have.property('name', 'Supply Air Temperature');

      // Verify BMS object property was created
      sinon.assert.calledOnce(sails.models.bmsobjectproperty.create);
      const propertyCall = sails.models.bmsobjectproperty.create.getCall(0);
      propertyCall.args[0].should.have.property('propertyTag', 'presentValue');

      // Verify job status was updated
      sinon.assert.calledWith(CacheService.hmset, 'BACnetDiscovery:job:test-job-123');

      // Verify WebSocket notification was sent
      sinon.assert.calledOnce(SocketService.notifyJouleTrackPublicRoom);
      const socketCall = SocketService.notifyJouleTrackPublicRoom.getCall(0);
      socketCall.args[0].should.equal('test-site');
      socketCall.args[1].should.equal('bacnet-discovery-status');
      socketCall.args[2].should.have.property('status', 'completed');
    });

    it('should update existing BMS connector instead of creating new one', async function () {
      // Mock existing connector
      sails.models.bmsconnector.findOne.resolves({ id: 1, siteId: 'test-site' });
      sandbox.stub(sails.models.bmsconnector, 'updateOne').resolves();

      const mockJob = { id: 'queue-job-123', data: mockJobData };

      await queueService.processJob(mockJob);

      // Verify connector was updated, not created
      sinon.assert.notCalled(sails.models.bmsconnector.create);
      sinon.assert.calledOnce(sails.models.bmsconnector.updateOne);
    });

    it('should handle database errors and update job status to failed', async function () {
      // Mock database error
      sails.models.bmsconnector.create.rejects(new Error('Database connection failed'));

      const mockJob = { id: 'queue-job-123', data: mockJobData };

      try {
        await queueService.processJob(mockJob);
        throw new Error('Expected processJob to throw');
      } catch (error) {
        error.message.should.equal('Database connection failed');

        // Verify job status was updated to failed
        sinon.assert.calledWith(CacheService.hmset, 'BACnetDiscovery:job:test-job-123');

        // Verify failure notification was sent
        sinon.assert.calledOnce(SocketService.notifyJouleTrackPublicRoom);
        const socketCall = SocketService.notifyJouleTrackPublicRoom.getCall(0);
        socketCall.args[2].should.have.property('status', 'failed');
      }
    });

    it('should handle invalid points data', async function () {
      const invalidJobData = {
        ...mockJobData,
        points: { invalid: 'data' } // Missing devices array
      };

      const mockJob = { id: 'queue-job-123', data: invalidJobData };

      try {
        await queueService.processJob(mockJob);
        throw new Error('Expected processJob to throw');
      } catch (error) {
        error.message.should.equal('Invalid points data: devices array is required');
      }
    });

    it('should continue processing other devices when one device fails', async function () {
      // Add a second device to test partial failure
      const jobDataWithMultipleDevices = {
        ...mockJobData,
        points: {
          ...mockJobData.points,
          devices: [
            ...mockJobData.points.devices,
            {
              name: 'AHU-02',
              address: '*************:47808',
              systemStatus: 'operational',
              description: 'Air Handling Unit 02',
              vendorName: 'Johnson Controls',
              modelName: 'AHU-Model-Y',
              location: 'Roof Level 2',
              objects: []
            }
          ]
        }
      };

      // Mock first device creation to fail, second to succeed
      sails.models.bmsdevice.create
        .onFirstCall().rejects(new Error('Device creation failed'))
        .onSecondCall().resolves({ id: 2 });

      const mockJob = { id: 'queue-job-123', data: jobDataWithMultipleDevices };

      const result = await queueService.processJob(mockJob);

      // Should still complete successfully with partial results
      result.should.have.property('success', true);
      result.should.have.property('devicesProcessed', 1); // Only one device processed successfully
    });
  });

  describe('Job Status Management', function () {

    it('should get job status from Redis', async function () {
      const mockJobData = {
        jobId: 'test-job-123',
        status: 'completed',
        siteId: 'test-site',
        controllerId: 'test-controller'
      };

      sandbox.stub(CacheService, 'hgetall').resolves(mockJobData);

      const status = await queueService.getJobStatus('test-job-123');

      status.should.deep.equal(mockJobData);
      sinon.assert.calledWith(CacheService.hgetall, 'BACnetDiscovery:job:test-job-123');
    });

    it('should handle Redis errors when getting job status', async function () {
      sandbox.stub(CacheService, 'hgetall').rejects(new Error('Redis connection failed'));

      try {
        await queueService.getJobStatus('test-job-123');
        throw new Error('Expected getJobStatus to throw');
      } catch (error) {
        error.message.should.equal('Redis connection failed');
      }
    });
  });

  describe('End-to-End Discovery Flow', function () {

    it('should complete full discovery flow from request to result processing', async function () {
      // This test simulates the complete flow:
      // 1. Discovery request triggers job creation
      // 2. External system sends discovery results
      // 3. Results are processed and persisted to database
      // 4. WebSocket notifications are sent

      const siteId = 'e2e-test-site';
      const controllerId = 'e2e-test-controller';

      // Step 1: Mock discovery request setup
      sandbox.stub(sails.models.sites, 'findOne').resolves({
        id: siteId,
        siteName: 'E2E Test Site'
      });

      sandbox.stub(sails.models.devices, 'findOne')
        .onFirstCall().resolves({
          deviceId: controllerId,
          siteId: siteId,
          isSlaveController: 1,
          deviceType: 'n3uronbacnetmqtt-controller'
        })
        .onSecondCall().resolves({
          deviceId: 'primary-joulebox',
          siteId: siteId,
          deviceType: 'joulebox',
          type: 'Primary'
        });

      // Mock Redis operations for job lifecycle
      let jobId;
      sandbox.stub(CacheService, 'get').callsFake((key) => {
        if (key.includes('activeJob:BACnetDiscovery')) {
          return Promise.resolve(null); // No active job initially
        }
        if (key.includes('activeJob:BACnetDiscovery') && jobId) {
          return Promise.resolve(jobId); // Return job ID for validation
        }
        return Promise.resolve(null);
      });

      sandbox.stub(CacheService, 'setex').resolves('OK');
      sandbox.stub(CacheService, 'expire').resolves(1);

      let jobData = {};
      sandbox.stub(CacheService, 'hgetall').callsFake((key) => {
        if (key.includes('BACnetDiscovery:job:') && jobId) {
          return Promise.resolve({
            jobId: jobId,
            status: 'pending',
            siteId: siteId,
            controllerId: controllerId,
            createdAt: new Date().toISOString()
          });
        }
        return Promise.resolve({});
      });

      // Mock IoT Core publish
      sandbox.stub(IotCoreService, 'publish').resolves(true);

      // Step 2: Simulate discovery request
      const request = require('supertest');
      const requestResponse = await new Promise((resolve, reject) => {
        request(sails.hooks.http.app)
          .post('/bacnet/discovery-request')
          .send({ siteId, controllerId })
          .expect(200)
          .end((err, res) => {
            if (err) return reject(err);
            resolve(res);
          });
      });

      // Extract job ID from response
      jobId = requestResponse.body.jobId;
      jobId.should.be.a('string');

      // Verify IoT Core publish was called with correct topic
      sinon.assert.calledOnce(IotCoreService.publish);
      const publishCall = IotCoreService.publish.getCall(0);
      publishCall.args[0].should.equal(`${siteId}/config/${controllerId}/discovery-request`);

      // Step 3: Simulate discovery result processing
      const discoveryResults = {
        siteId,
        controllerId,
        jobId,
        points: {
          protocol: 'bacnet',
          version: 'v1',
          bmsConnectorClass: 'n3uron/bacnet/mqtt',
          devices: [
            {
              name: 'E2E-AHU-01',
              address: '*************:47808',
              systemStatus: 'operational',
              description: 'End-to-End Test AHU',
              vendorName: 'Test Vendor',
              modelName: 'Test Model',
              location: 'Test Location',
              objects: [
                {
                  name: 'Test Temperature Sensor',
                  address: '*************:47808:analogInput:1',
                  description: 'Test temperature sensor',
                  eventState: 'normal',
                  outOfService: 'false',
                  reliability: 'noFaultDetected',
                  statusFlags: { inAlarm: false, fault: false, overridden: false, outOfService: false },
                  protocolNativeUnit: 'degreesCelsius',
                  unit: '°C',
                  properties: [
                    {
                      propertyTag: 'presentValue',
                      type: 'real',
                      address: '*************:47808:analogInput:1:presentValue',
                      protocolNativeUnit: 'degreesCelsius',
                      value: '23.5'
                    }
                  ]
                }
              ]
            }
          ]
        }
      };

      // Mock queue service to process the job
      const mockJob = { id: 'queue-job-e2e', data: discoveryResults };
      const processResult = await queueService.processJob(mockJob);

      // Step 4: Verify complete flow results
      processResult.should.have.property('success', true);
      processResult.should.have.property('jobId', jobId);
      processResult.should.have.property('devicesProcessed', 1);

      // Verify database persistence
      sinon.assert.calledOnce(sails.models.bmsconnector.create);
      sinon.assert.calledOnce(sails.models.bmsdevice.create);
      sinon.assert.calledOnce(sails.models.bmsdeviceobject.create);
      sinon.assert.calledOnce(sails.models.bmsobjectproperty.create);

      // Verify WebSocket notification
      sinon.assert.calledOnce(SocketService.notifyJouleTrackPublicRoom);
      const socketCall = SocketService.notifyJouleTrackPublicRoom.getCall(0);
      socketCall.args[0].should.equal(siteId);
      socketCall.args[1].should.equal('bacnet-discovery-status');
      socketCall.args[2].should.have.property('status', 'completed');
      socketCall.args[2].should.have.property('jobId', jobId);

      // Verify job status updates
      sinon.assert.called(CacheService.hmset);

      // Verify active job cleanup
      sinon.assert.called(CacheService.del);
    });
  });
});
