const moment = require('moment-timezone');

/**
 * BACnet Discovery Error Handler Utility
 * Provides centralized error handling for BACnet discovery operations
 */
class BACnetErrorHandler {
  
  /**
   * Handle discovery request errors
   * @param {Error} error - The error object
   * @param {string} siteId - Site ID
   * @param {string} controllerId - Controller ID
   * @param {string} jobId - Job ID (optional)
   * @returns {object} Formatted error response
   */
  static handleDiscoveryRequestError(error, siteId, controllerId, jobId = null) {
    const timestamp = moment.tz('UTC').toISOString();
    
    sails.log.error(`BACnet discovery request error - Site: ${siteId}, Controller: ${controllerId}`, {
      error: error.message,
      stack: error.stack,
      jobId,
      timestamp
    });

    // Categorize error types
    if (error.name === 'ValidationError') {
      return {
        statusCode: 400,
        error: 'Validation Error',
        message: error.message,
        details: sails.config.environment === 'development' ? error.stack : undefined
      };
    }

    if (error.name === 'NotFoundError' || error.message.includes('not found')) {
      return {
        statusCode: 404,
        error: 'Resource Not Found',
        message: error.message,
        details: sails.config.environment === 'development' ? error.stack : undefined
      };
    }

    if (error.name === 'ConflictError' || error.message.includes('already in progress')) {
      return {
        statusCode: 409,
        error: 'Conflict',
        message: error.message,
        details: sails.config.environment === 'development' ? error.stack : undefined
      };
    }

    // Default to server error
    return {
      statusCode: 500,
      error: 'Internal Server Error',
      message: 'Failed to process BACnet discovery request',
      details: sails.config.environment === 'development' ? error.message : undefined
    };
  }

  /**
   * Handle discovery result processing errors
   * @param {Error} error - The error object
   * @param {string} siteId - Site ID
   * @param {string} controllerId - Controller ID
   * @param {string} jobId - Job ID
   * @returns {object} Formatted error response
   */
  static handleDiscoveryResultError(error, siteId, controllerId, jobId) {
    const timestamp = moment.tz('UTC').toISOString();
    
    sails.log.error(`BACnet discovery result error - Site: ${siteId}, Controller: ${controllerId}, Job: ${jobId}`, {
      error: error.message,
      stack: error.stack,
      timestamp
    });

    // Update job status to failed in Redis
    this.updateJobStatusToFailed(jobId, error.message).catch(redisError => {
      sails.log.error('Failed to update job status in Redis:', redisError);
    });

    return {
      statusCode: 500,
      error: 'Processing Error',
      message: 'Failed to process BACnet discovery result',
      jobId,
      details: sails.config.environment === 'development' ? error.message : undefined
    };
  }

  /**
   * Handle queue processing errors
   * @param {Error} error - The error object
   * @param {string} jobId - Job ID
   * @param {object} jobData - Job data
   * @returns {object} Formatted error response
   */
  static handleQueueProcessingError(error, jobId, jobData = {}) {
    const timestamp = moment.tz('UTC').toISOString();
    
    sails.log.error(`BACnet queue processing error - Job: ${jobId}`, {
      error: error.message,
      stack: error.stack,
      jobData,
      timestamp
    });

    // Categorize database errors
    if (error.name === 'DatabaseError' || error.code) {
      return {
        type: 'database_error',
        message: 'Database operation failed',
        originalError: error.message,
        code: error.code
      };
    }

    // Categorize validation errors
    if (error.name === 'ValidationError' || error.message.includes('validation')) {
      return {
        type: 'validation_error',
        message: 'Data validation failed',
        originalError: error.message
      };
    }

    // Default processing error
    return {
      type: 'processing_error',
      message: 'Failed to process discovery data',
      originalError: error.message
    };
  }

  /**
   * Handle Redis/Cache errors
   * @param {Error} error - The error object
   * @param {string} operation - The operation that failed
   * @param {string} key - The Redis key involved
   */
  static handleCacheError(error, operation, key) {
    sails.log.error(`Redis cache error - Operation: ${operation}, Key: ${key}`, {
      error: error.message,
      stack: error.stack,
      timestamp: moment.tz('UTC').toISOString()
    });

    // Don't throw cache errors, just log them
    // The system should continue to function even if cache is unavailable
  }

  /**
   * Handle IoT Core/MQTT errors
   * @param {Error} error - The error object
   * @param {string} topic - The MQTT topic
   * @param {object} payload - The payload that failed to send
   */
  static handleIoTCoreError(error, topic, payload) {
    sails.log.error(`IoT Core MQTT error - Topic: ${topic}`, {
      error: error.message,
      stack: error.stack,
      payload,
      timestamp: moment.tz('UTC').toISOString()
    });

    throw new Error(`Failed to publish to IoT Core: ${error.message}`);
  }

  /**
   * Handle WebSocket notification errors
   * @param {Error} error - The error object
   * @param {string} siteId - Site ID
   * @param {string} topic - WebSocket topic
   * @param {object} data - Data that failed to send
   */
  static handleWebSocketError(error, siteId, topic, data) {
    sails.log.error(`WebSocket notification error - Site: ${siteId}, Topic: ${topic}`, {
      error: error.message,
      stack: error.stack,
      data,
      timestamp: moment.tz('UTC').toISOString()
    });

    // Don't throw WebSocket errors, just log them
    // The system should continue to function even if notifications fail
  }

  /**
   * Update job status to failed in Redis
   * @param {string} jobId - Job ID
   * @param {string} errorMessage - Error message
   */
  static async updateJobStatusToFailed(jobId, errorMessage) {
    try {
      const jobKey = `BACnetDiscovery:job:${jobId}`;
      await CacheService.hmset(jobKey, {
        status: 'failed',
        error: errorMessage,
        failedAt: moment.tz('UTC').toISOString()
      });
    } catch (redisError) {
      sails.log.error('Failed to update job status in Redis:', redisError);
    }
  }

  /**
   * Validate discovery request inputs
   * @param {string} siteId - Site ID
   * @param {string} controllerId - Controller ID
   * @throws {Error} If validation fails
   */
  static validateDiscoveryRequest(siteId, controllerId) {
    if (!siteId || typeof siteId !== 'string') {
      throw new Error('Site ID is required and must be a string');
    }

    if (!controllerId || typeof controllerId !== 'string') {
      throw new Error('Controller ID is required and must be a string');
    }

    // Validate format (basic alphanumeric with hyphens)
    const validFormat = /^[a-zA-Z0-9-_]+$/;
    
    if (!validFormat.test(siteId)) {
      throw new Error('Site ID contains invalid characters');
    }

    if (!validFormat.test(controllerId)) {
      throw new Error('Controller ID contains invalid characters');
    }
  }

  /**
   * Validate discovery result inputs
   * @param {string} siteId - Site ID
   * @param {string} controllerId - Controller ID
   * @param {string} jobId - Job ID
   * @param {object} points - Discovery points data
   * @throws {Error} If validation fails
   */
  static validateDiscoveryResult(siteId, controllerId, jobId, points) {
    this.validateDiscoveryRequest(siteId, controllerId);

    if (!jobId || typeof jobId !== 'string') {
      throw new Error('Job ID is required and must be a string');
    }

    if (!points || typeof points !== 'object') {
      throw new Error('Points data is required and must be an object');
    }

    if (!points.devices || !Array.isArray(points.devices)) {
      throw new Error('Points data must contain a devices array');
    }

    if (points.devices.length === 0) {
      throw new Error('Points data must contain at least one device');
    }

    // Validate each device has required fields
    points.devices.forEach((device, index) => {
      if (!device.name || !device.address) {
        throw new Error(`Device at index ${index} is missing required fields (name, address)`);
      }
    });
  }
}

module.exports = BACnetErrorHandler;
