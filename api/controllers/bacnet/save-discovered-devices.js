const moment = require('moment-timezone');
const BACnetErrorHandler = require('../../utils/bacnet/error-handler.util');

/**
 * Save discovered BACnet devices as third-party master controllers
 * This API handles the "Save" functionality from the discovery UI
 */
module.exports = {
  friendlyName: 'Save Discovered BACnet Devices',
  description: 'Save discovered BACnet devices as third-party master controllers in the system',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be saved'
    },

    controllerId: {
      type: 'string',
      required: true,
      description: 'BACnet slave controller ID that performed the discovery'
    },

    jobId: {
      type: 'string',
      required: true,
      description: 'Discovery job ID'
    },

    selectedDevices: {
      type: 'ref',
      required: true,
      description: 'Array of selected BACnet devices to save as controllers',
      example: [
        {
          address: '*************:47808',
          name: 'AHU-01',
          description: 'Air Handling Unit 01',
          vendorName: 'Johnson Controls',
          modelName: 'AHU-Model-X',
          location: 'Roof Level 1',
          systemStatus: 'operational',
          networkId: 'bacnet-network-1',
          regionId: 'hvac-region',
          areaId: 'roof-area'
        }
      ]
    }
  },

  exits: {
    success: {
      description: 'Devices saved successfully',
      outputType: 'ref'
    },

    badRequest: {
      description: 'Invalid input parameters',
      responseType: 'badRequest'
    },

    notFound: {
      description: 'Site, controller, or job not found',
      responseType: 'notFound'
    },

    conflict: {
      description: 'Device already exists or other conflict',
      responseType: 'conflict'
    },

    serverError: {
      description: 'Internal server error',
      responseType: 'serverError'
    }
  },

  fn: async function (inputs, exits) {
    const { siteId, controllerId, jobId, selectedDevices } = inputs;

    try {
      // Validate inputs
      BACnetErrorHandler.validateDiscoveryRequest(siteId, controllerId);

      if (!jobId || typeof jobId !== 'string') {
        throw new Error('Job ID is required and must be a string');
      }

      if (!selectedDevices || !Array.isArray(selectedDevices) || selectedDevices.length === 0) {
        throw new Error('Selected devices array is required and must not be empty');
      }

      sails.log.info(`Saving ${selectedDevices.length} discovered BACnet devices for site: ${siteId}, job: ${jobId}`);

      // Validate site exists
      const site = await sails.models.sites.findOne({ id: siteId });
      if (!site) {
        throw Object.assign(new Error('Site not found'), { name: 'NotFoundError' });
      }

      // Validate BACnet slave controller exists
      const slaveController = await sails.models.devices.findOne({
        deviceId: controllerId,
        siteId: siteId,
        isSlaveController: 1
      });

      if (!slaveController) {
        throw Object.assign(new Error('BACnet slave controller not found'), { name: 'NotFoundError' });
      }

      // Find the primary controller (JouleBox) for this site
      const primaryController = await sails.models.devices.findOne({
        siteId: siteId,
        deviceType: 'joulebox',
        type: 'Primary'
      });

      if (!primaryController) {
        throw Object.assign(new Error('Primary controller (JouleBox) not found for site'), { name: 'NotFoundError' });
      }

      // Validate job exists and is completed
      const jobKey = `BACnetDiscovery:job:${jobId}`;
      const jobData = await CacheService.hgetall(jobKey);

      if (!jobData || !jobData.jobId) {
        throw Object.assign(new Error('Discovery job not found'), { name: 'NotFoundError' });
      }

      if (jobData.status !== 'completed') {
        throw Object.assign(new Error('Discovery job is not completed yet'), { name: 'ConflictError' });
      }

      // Get next device IDs for the new controllers
      const deviceIdCount = await sails.models.dynamokeystore.findOne({ key: 'totalDeviceCount' });
      let nextDeviceId = deviceIdCount ? parseInt(deviceIdCount.value) + 1 : 1;

      const savedDevices = [];
      const errors = [];

      // Process each selected device
      for (const deviceData of selectedDevices) {
        try {
          // Validate required fields for each device
          if (!deviceData.address || !deviceData.name) {
            throw new Error(`Device missing required fields: address and name are required`);
          }

          // Check if device with this address already exists
          const existingDevice = await sails.models.devices.findOne({
            siteId: siteId,
            name: deviceData.name
          });

          if (existingDevice) {
            errors.push({
              device: deviceData,
              error: `Device with name '${deviceData.name}' already exists`
            });
            continue;
          }

          // Validate network and region exist in site
          if (deviceData.networkId && !site.networks[deviceData.networkId]) {
            throw new Error(`Network '${deviceData.networkId}' does not exist in site`);
          }

          if (deviceData.regionId && !site.regions[deviceData.regionId]) {
            throw new Error(`Region '${deviceData.regionId}' does not exist in site`);
          }

          // Create device configuration for discovered BACnet third-party controller
          const deviceConfig = {
            deviceId: String(nextDeviceId),
            siteId: siteId,
            name: deviceData.name,
            deviceType: 'n3uronbacnetmqtt', // From vendors.json - UI recognition only
            networkId: deviceData.networkId || Object.keys(site.networks)[0],
            regionId: deviceData.regionId || Object.keys(site.regions)[0],
            areaId: deviceData.areaId || Object.keys(site.areas || {})[0],

            // BACnet device information
            IPAddress: deviceData.address.split(':')[0], // Extract IP from address
            vendorId: 'n3uronbacnetmqtt',
            hardwareVer: deviceData.modelName || 'Unknown',
            softwareVer: 'v1',
            operationMode: 'network',

            // Third-party master controller configuration
            isSlaveController: false, // This is a MASTER controller (0 = master, 1 = slave)
            controllerId: primaryController.deviceId, // Reports to primary controller (JouleBox)
            secondaryControllerId: controllerId, // Communication path via N3uron slave controller
            communicationType: 'IoT', // No direct communication - managed via parent controller
            communicationCategory: 'IoT',
            driverType: '0', // No driver needed - third-party controller
            functionType: 'IoT',

            // Additional metadata
            deviceMeta: {
              bacnetAddress: deviceData.address,
              systemStatus: deviceData.systemStatus,
              description: deviceData.description,
              vendorName: deviceData.vendorName,
              modelName: deviceData.modelName,
              location: deviceData.location,
              discoveredAt: moment.tz('UTC').toISOString(),
              discoveryJobId: jobId,
              discoveredBy: controllerId, // N3uron slave controller that discovered it
              discoveryMethod: 'bacnet_n3uron' // For future device creation reference
            },

            status: 1,
            isConfigured: '1'
          };

          // Create the device
          const newDevice = await sails.models.devices.create(deviceConfig);

          // Update site networks and regions to include this controller
          const updatedSite = { ...site };

          if (updatedSite.networks[deviceConfig.networkId]) {
            if (!updatedSite.networks[deviceConfig.networkId].includes(String(nextDeviceId))) {
              updatedSite.networks[deviceConfig.networkId].push(String(nextDeviceId));
            }
          }

          if (updatedSite.regions[deviceConfig.regionId] && updatedSite.regions[deviceConfig.regionId].controller) {
            if (!updatedSite.regions[deviceConfig.regionId].controller.includes(String(nextDeviceId))) {
              updatedSite.regions[deviceConfig.regionId].controller.push(String(nextDeviceId));
            }
          }

          // Update site with new controller references
          await sails.models.sites.updateOne({ id: siteId }).set({
            networks: updatedSite.networks,
            regions: updatedSite.regions
          });

          // Update total device count
          await sails.models.dynamokeystore.updateOne({ key: 'totalDeviceCount' }).set({ value: String(nextDeviceId) });

          // Update site config timestamp
          await sails.models.dynamokeystore.updateOne({ key: `configTs:${siteId}` }).set({
            value: moment.tz('UTC').toISOString()
          });

          savedDevices.push({
            deviceId: newDevice.deviceId,
            name: newDevice.name,
            address: deviceData.address,
            status: 'created'
          });

          nextDeviceId++;

          sails.log.info(`Created BACnet controller device: ${newDevice.deviceId} - ${newDevice.name}`);

        } catch (deviceError) {
          sails.log.error(`Error creating device ${deviceData.name}:`, deviceError);
          errors.push({
            device: deviceData,
            error: deviceError.message
          });
        }
      }

      // Send WebSocket notification about new devices
      if (savedDevices.length > 0) {
        await SocketService.notifyJouleTrackPublicRoom(siteId, 'devices', {
          event: 'create',
          data: savedDevices.map(d => ({ deviceId: d.deviceId, name: d.name }))
        });
      }

      // Update job status to indicate devices were saved
      await CacheService.hmset(jobKey, {
        devicesSaved: savedDevices.length,
        savedAt: moment.tz('UTC').toISOString(),
        savedDevices: JSON.stringify(savedDevices.map(d => d.deviceId))
      });

      const response = {
        message: `Successfully saved ${savedDevices.length} BACnet devices as controllers`,
        savedDevices,
        errors: errors.length > 0 ? errors : undefined,
        summary: {
          total: selectedDevices.length,
          saved: savedDevices.length,
          failed: errors.length
        }
      };

      sails.log.info(`BACnet device save operation completed for job ${jobId}: ${savedDevices.length} saved, ${errors.length} failed`);

      return exits.success(response);

    } catch (error) {
      const errorResponse = BACnetErrorHandler.handleDiscoveryRequestError(error, siteId, controllerId, jobId);

      // Return appropriate exit based on status code
      switch (errorResponse.statusCode) {
        case 400:
          return exits.badRequest(errorResponse);
        case 404:
          return exits.notFound(errorResponse);
        case 409:
          return exits.conflict(errorResponse);
        default:
          return exits.serverError(errorResponse);
      }
    }
  }
};
