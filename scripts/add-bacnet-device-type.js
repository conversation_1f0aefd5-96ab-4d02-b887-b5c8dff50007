/**
 * <PERSON><PERSON><PERSON> to add BACnet controller device type to the database
 * Run this script to add the n3uronbacnetmqtt device type configuration
 */

const moment = require('moment-timezone');

// Set timezone
moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

module.exports = {
  friendlyName: 'Add BACnet Device Type',
  description: 'Add n3uronbacnetmqtt device type configuration to the database',

  fn: async function () {
    try {
      console.log('Adding BACnet controller device type...');

      // Check if device type already exists
      const existingDeviceType = await DeviceTypes.findOne({
        deviceType: 'n3uronbacnetmqtt',
        driverType: '0'
      });

      if (existingDeviceType) {
        console.log('BACnet device type already exists, skipping...');
        return {
          success: true,
          message: 'Device type already exists'
        };
      }

      // Define the BACnet controller device type configuration
      const bacnetDeviceType = {
        deviceType: 'n3uronbacnetmqtt',
        driverType: '0',
        driverName: 'N3uron BACnet MQTT Controller',
        communicationCategory: 'IoT',
        communicationType: 'IoT',
        class: 'controllers',
        functionType: 'IoT',
        mbBatchReadEnable: '0',
        svgIds: [],
        params: {
          description: 'BACnet controller discovered via N3uron MQTT integration',
          vendor: 'n3uronbacnetmqtt',
          protocol: 'bacnet',
          connectionType: 'mqtt'
        },
        parameters: [
          // Basic controller status parameters
          {
            abbr: 'status',
            displayName: 'Controller Status',
            unit: '',
            type: 'data',
            address: 'status',
            paramGroup: 'status',
            utilityType: 'status',
            mode: 'read',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '1',
            mulFactor: '1',
            offset: '0',
            filter_existence: '1',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          {
            abbr: 'lastSeen',
            displayName: 'Last Seen',
            unit: 'timestamp',
            type: 'data',
            address: 'lastSeen',
            paramGroup: 'status',
            utilityType: 'timestamp',
            mode: 'read',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '999999999999',
            mulFactor: '1',
            offset: '0',
            filter_existence: '1',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          {
            abbr: 'deviceCount',
            displayName: 'Connected Devices',
            unit: 'count',
            type: 'data',
            address: 'deviceCount',
            paramGroup: 'status',
            utilityType: 'count',
            mode: 'read',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '1000',
            mulFactor: '1',
            offset: '0',
            filter_existence: '1',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          {
            abbr: 'networkStatus',
            displayName: 'Network Status',
            unit: '',
            type: 'data',
            address: 'networkStatus',
            paramGroup: 'network',
            utilityType: 'status',
            mode: 'read',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '1',
            mulFactor: '1',
            offset: '0',
            filter_existence: '1',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          // Discovery control commands
          {
            abbr: 'startDiscovery',
            displayName: 'Start Discovery',
            unit: '',
            type: 'command',
            address: 'discovery/start',
            paramGroup: 'discovery',
            utilityType: 'command',
            mode: 'write',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '1',
            mulFactor: '1',
            offset: '0',
            filter_existence: '0',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          {
            abbr: 'stopDiscovery',
            displayName: 'Stop Discovery',
            unit: '',
            type: 'command',
            address: 'discovery/stop',
            paramGroup: 'discovery',
            utilityType: 'command',
            mode: 'write',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '1',
            mulFactor: '1',
            offset: '0',
            filter_existence: '0',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          },
          {
            abbr: 'discoveryStatus',
            displayName: 'Discovery Status',
            unit: '',
            type: 'data',
            address: 'discovery/status',
            paramGroup: 'discovery',
            utilityType: 'status',
            mode: 'read',
            regType: 0,
            functionCode: '0',
            min: '0',
            max: '3',
            mulFactor: '1',
            offset: '0',
            filter_existence: '1',
            filter_oldVal: '0',
            filter_variance: '0',
            tolerance: '0',
            decimalPrecision: '0'
          }
        ]
      };

      // Convert parameters to string format as expected by the database
      bacnetDeviceType.parameters = bacnetDeviceType.parameters.map(param => JSON.stringify(param));

      // Create the device type
      const createdDeviceType = await DeviceTypes.create(bacnetDeviceType);

      console.log('BACnet device type created successfully:', createdDeviceType.deviceType);

      return {
        success: true,
        message: 'BACnet device type added successfully',
        deviceType: createdDeviceType
      };

    } catch (error) {
      console.error('Error adding BACnet device type:', error);
      throw error;
    }
  }
};

// If running directly (not as a helper)
if (require.main === module) {
  (async () => {
    try {
      // Initialize Sails if not already initialized
      if (!global.sails) {
        const sails = require('sails');
        await sails.lift({
          // Configure for script execution
          hooks: {
            grunt: false,
            sockets: false,
            pubsub: false
          },
          log: {
            level: 'error'
          }
        });
      }

      const result = await module.exports.fn();
      console.log('Script completed:', result);
      
      if (global.sails) {
        await sails.lower();
      }
      
      process.exit(0);
    } catch (error) {
      console.error('Script failed:', error);
      
      if (global.sails) {
        await sails.lower();
      }
      
      process.exit(1);
    }
  })();
}
